# 通用支付工具使用文档

## 概述

`utils/paymentUtils.js` 提供了一套通用的支付解决方案，自动适配微信小程序和APP环境，简化支付流程的开发和维护。

## 功能特性

- ✅ **自动平台检测**：自动识别微信小程序、APP、H5环境
- ✅ **统一API接口**：一套代码适配多个平台
- ✅ **完整错误处理**：支付成功、失败、取消的完整处理
- ✅ **消息订阅支持**：可选的微信消息订阅功能
- ✅ **灵活配置**：支持自定义回调和页面跳转
- ✅ **简化调用**：提供快速调用方法

## 安装使用

### 1. 导入工具函数

```javascript
// 完整导入
import { handleUniversalPayment, quickPay, getCurrentPlatform } from '@/utils/paymentUtils.js';

// 或者默认导入
import PaymentUtils from '@/utils/paymentUtils.js';
```

### 2. 基础使用

#### 最简单的调用方式

```javascript
// 只需要支付数据和成功跳转页面
quickPay(paymentData, '/user/order_list?tab=0');
```

#### 标准调用方式

```javascript
handleUniversalPayment(paymentData, {
  successRedirectUrl: '/user/order_list?tab=0',
  onSuccess: (res) => {
    console.log('支付成功', res);
  }
});
```

### 3. 完整配置示例

```javascript
handleUniversalPayment(paymentData, {
  // 成功回调
  onSuccess: (res) => {
    console.log('支付成功', res);
    // 自定义成功处理逻辑
  },
  
  // 失败回调
  onFail: (err) => {
    console.error('支付失败', err);
    // 自定义失败处理逻辑
  },
  
  // 取消回调
  onCancel: () => {
    console.log('用户取消支付');
    // 自定义取消处理逻辑
  },
  
  // 成功后跳转页面
  successRedirectUrl: '/user/order_list?tab=0',
  
  // 是否显示成功提示（默认true）
  showSuccessToast: true,
  
  // 是否显示失败提示（默认true）
  showFailToast: true,
  
  // 是否启用消息订阅（默认false）
  enableSubscribe: true,
  
  // 消息订阅模板ID
  tmplIds: 'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'
});
```

## API 参考

### handleUniversalPayment(paymentData, options)

主要的支付处理函数

#### 参数

**paymentData** (Object) - 后端返回的支付数据
- `appId` (string) - 微信应用ID
- `nonceStr` (string) - 随机字符串
- `prepayId` (string) - 预支付ID
- `partnerId` (string) - 商户ID
- `timestamp` (number) - 时间戳
- `sign` (string) - 签名

**options** (Object) - 配置选项
- `onSuccess` (Function) - 支付成功回调
- `onFail` (Function) - 支付失败回调
- `onCancel` (Function) - 支付取消回调
- `successRedirectUrl` (string) - 成功后跳转页面
- `showSuccessToast` (boolean) - 是否显示成功提示，默认true
- `showFailToast` (boolean) - 是否显示失败提示，默认true
- `enableSubscribe` (boolean) - 是否启用消息订阅，默认false
- `tmplIds` (string|Array) - 消息订阅模板ID

### quickPay(paymentData, successRedirectUrl, onSuccess)

简化版支付函数

#### 参数

- `paymentData` (Object) - 支付数据
- `successRedirectUrl` (string) - 成功后跳转页面
- `onSuccess` (Function) - 可选的成功回调

### getCurrentPlatform()

获取当前运行平台

#### 返回值

- `'app-plus'` - APP环境
- `'mp-weixin'` - 微信小程序
- `'h5'` - H5环境
- `'unknown'` - 未知环境

## 实际应用示例

### 1. 订单支付页面

```javascript
// 原来的代码
confirmPay() {
  this.$api.service.nowPay({
    orderId: this.id,
    couponId: this.confirmCou ? this.confirmCou.couponId : 0,
    type: 1
  }).then(res => {
    if(res.code === '-1') {
      uni.showToast({
        title: res.msg,
        icon: 'none'
      });
    } else {
      // 复杂的支付处理逻辑...
    }
  });
}

// 使用工具函数后
import { handleUniversalPayment } from '@/utils/paymentUtils.js';

confirmPay() {
  this.$api.service.nowPay({
    orderId: this.id,
    couponId: this.confirmCou ? this.confirmCou.couponId : 0,
    type: 1
  }).then(res => {
    if(res.code === '-1') {
      uni.showToast({
        title: res.msg,
        icon: 'none'
      });
    } else {
      // 使用通用支付工具
      handleUniversalPayment(res.data, {
        successRedirectUrl: '/user/order_list?tab=0',
        enableSubscribe: true,
        tmplIds: this.tmplIds
      });
    }
  });
}
```

### 2. 保证金支付

```javascript
import { quickPay } from '@/utils/paymentUtils.js';

confirmPay() {
  this.$api.shifu.nowPay({
    payPrice: 200,
    couponId: 0,
    type: 1
  }).then(res => {
    if(res.code === '-1') {
      uni.showToast({
        title: res.msg,
        icon: 'none'
      });
    } else {
      // 简化调用
      quickPay(res.data, '/shifu/index');
    }
  });
}
```

### 3. 活动支付

```javascript
import { handleUniversalPayment } from '@/utils/paymentUtils.js';

confirmPay() {
  this.$api.service.huodongPay({
    // 支付参数
  }).then(res => {
    if(res.code === '-1') {
      uni.showToast({
        title: res.msg,
        icon: 'none'
      });
    } else {
      handleUniversalPayment(res.data, {
        successRedirectUrl: '/user/tiaozhuan',
        onSuccess: (payRes) => {
          // 支付成功后的特殊处理
          console.log('活动支付成功', payRes);
        }
      });
    }
  });
}
```

## 错误处理

工具函数会自动处理以下情况：

1. **支付成功**：显示成功提示，执行回调，页面跳转
2. **支付失败**：显示失败提示，执行失败回调
3. **用户取消**：显示取消提示，执行取消回调
4. **平台适配**：自动选择合适的支付方式

## 注意事项

1. **支付数据格式**：确保后端返回的支付数据包含必要字段
2. **消息订阅**：只在微信小程序环境下有效
3. **页面跳转**：使用 `uni.redirectTo`，会替换当前页面
4. **错误日志**：所有错误都会在控制台输出详细信息

## 迁移指南

### 替换现有支付代码

1. **导入工具函数**
2. **保留API调用逻辑**
3. **替换支付处理部分**
4. **移除平台判断代码**
5. **简化错误处理**

### 迁移前后对比

**迁移前**：
- 需要手动判断平台
- 重复的支付处理代码
- 复杂的错误处理逻辑
- 代码量大，维护困难

**迁移后**：
- 自动平台适配
- 统一的支付接口
- 标准化错误处理
- 代码简洁，易于维护

## 版本信息

- **版本**：1.0.0
- **更新日期**：2025-01-08
- **兼容性**：uni-app 全平台
- **依赖**：无额外依赖
