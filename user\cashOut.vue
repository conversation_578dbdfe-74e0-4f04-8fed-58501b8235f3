<template>
	<view class="page">
		<view class="header">
			<view class="left">提现至</view>
			<view class="right" @tap="showCashToType = true">
				{{ getCashToTypeName() }}
				<text class="arrow">></text>
			</view>
		</view>

		<!-- 支付宝提现信息输入 -->
		<view class="alipay-info" v-if="cashToType === 2">
			<view class="info-item">
				<view class="label">支付宝手机号</view>
				<u--input
					placeholder="请输入支付宝登录手机号"
					v-model="alipayPhone"
					border="none"
				></u--input>
			</view>
			<view class="info-item">
				<view class="label">真实姓名</view>
				<u--input
					placeholder="请输入真实姓名"
					v-model="realName"
					border="none"
				></u--input>
			</view>
			<view class="info-item">
				<view class="label">身份证号</view>
				<u--input
					placeholder="请输入身份证号"
					v-model="idCode"
					border="none"
				></u--input>
			</view>
		</view>

		<view class="mid">
			<view class="title">提现金额</view>
			<view class="top">
				<view class="t_left">
					<u--input
						placeholder="请输入提现金额"
						type="digit"
						border="none"
						v-model="money"
						@change="change"
						prefixIcon="rmb"
					></u--input>
				</view>
				<view class="r_left" @tap="goAll">全部提现</view>
			</view>
			<view class="bottom">可提现金额￥{{ allmoney }}</view>
		</view>
		<view class="btn" @tap="confirmTx" :disabled="isSubmitting">确认提现</view>
		<text class="tips">温馨提示：提现申请发起后，预计3个工作日内到账。</text>
		<text class="contact">有问题请联系客服 <text class="phone" @tap="copyPhoneNumber">4008326986</text></text>

		<!-- 提现渠道选择弹窗 -->
		<u-popup :show="showCashToType" mode="bottom" :round="10" closeable @close="showCashToType = false">
			<view class="cash-type-modal">
				<view class="modal-header">
					<view class="modal-title">选择提现渠道</view>
					<view class="modal-close" @tap="showCashToType = false">×</view>
				</view>
				<view class="cash-type-list">
					<view
						class="cash-type-item"
						:class="{ active: cashToType === 1 }"
						@tap="selectCashToType(1)"
					>
						<view class="type-name">微信</view>
						<view class="type-check" v-if="cashToType === 1">✓</view>
					</view>
					<view
						class="cash-type-item"
						:class="{ active: cashToType === 2 }"
						@tap="selectCashToType(2)"
					>
						<view class="type-name">支付宝</view>
						<view class="type-check" v-if="cashToType === 2">✓</view>
					</view>
					<view
						class="cash-type-item"
						:class="{ active: cashToType === 3 }"
						@tap="selectCashToType(3)"
					>
						<view class="type-name">银行卡</view>
						<view class="type-check" v-if="cashToType === 3">✓</view>
					</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
export default {
	data() {
		return {
			money: '',
			allmoney: '0',
			isSubmitting: false,
			mchId: '1648027588', // Replace with your actual Merchant ID
			cashToType: 1, // 提现渠道：1=微信，2=支付宝，3=银行卡
			showCashToType: false, // 是否显示提现渠道选择弹窗
			alipayPhone: '', // 支付宝登录手机号
			realName: '', // 真实姓名
			idCode: '', // 身份证号
		};
	},
	onPullDownRefresh() {
		console.log('refresh');
		setTimeout(function () {
			uni.stopPullDownRefresh();
		}, 1000);
	},
	methods: {
		async confirmTx() {
			const amount = Number(this.money);
			if (!amount || amount <= 0) {
				uni.showToast({
					title: '请输入有效的提现金额',
					icon: 'none',
				});
				return;
			}
			if (amount > Number(this.allmoney)) {
				uni.showToast({
					title: '超过可提现金额',
					icon: 'none',
				});
				return;
			}
			if (amount > 800) {
				uni.showToast({
					title: '最高提现金额为799元',
					icon: 'none',
				});
				return;
			}
			if (amount < 1) {
				uni.showToast({
					title: '最低提现金额为1元',
					icon: 'none',
				});
				return;
			}

			// 支付宝提现时验证必填信息
			if (this.cashToType === 2) {
				if (!this.alipayPhone) {
					uni.showToast({
						title: '请输入支付宝登录手机号',
						icon: 'none',
					});
					return;
				}
				if (!this.realName) {
					uni.showToast({
						title: '请输入真实姓名',
						icon: 'none',
					});
					return;
				}
				if (!this.idCode) {
					uni.showToast({
						title: '请输入身份证号',
						icon: 'none',
					});
					return;
				}
			}

			// Show confirmation modal before proceeding
			uni.showModal({
				title: '确认提现',
				content: '为确保您的账户余额准确无误，提现操作一旦提交，请不要中途退出或刷新页面，若您在提现过程中中止操作，可能会导致余额错误，需等待1-3个工作日处理您的请求。',
				confirmText: '确定',
				cancelText: '取消',
				success: async (res) => {
					console.log(res)
					if (res.confirm) {
						// Proceed with withdrawal only if user confirms
						// if (!uni.canIUse('requestMerchantTransfer')) {
						// 	uni.showModal({
						// 		content: '你的微信版本过低，请更新至最新版本。',
						// 		showCancel: false,
						// 	});
						// 	return;
						// }

						try {
							this.isSubmitting = true;

							// 构建请求参数
							const params = this.buildWithdrawParams();

							// Request signed package from backend
							const res = await this.$api.mine.applyWallet(params);

							if(res.code==='200'){
								uni.showToast({
									title: res.msg || '提现申请提交成功',
									icon: 'success',
								});
							this.getMoney();
								// 提现成功后跳转到提现记录页面
								setTimeout(() => {
									uni.navigateTo({
										url: '/user/coreWallet'
									});
								}, 1500);
							} else {
								uni.showToast({
									title: res.msg || '提现申请失败',
									icon: 'none',
								});
							}

						} catch (error) {
							uni.showToast({
								title: '请稍后重试',
								icon: 'none',
							});
							this.isSubmitting = false;
						}
					}
				},
			});
		},
		goAll() {
			this.money = this.allmoney;
		},
		change() {
			// Handle input change if needed
		},

		// 获取当前平台类型
		getCurrentPlatform() {
			// #ifdef APP-PLUS
			return 'app-plus';
			// #endif
			// #ifdef MP-WEIXIN
			return 'mp-weixin';
			// #endif
			// #ifdef H5
			return 'h5';
			// #endif
			return 'unknown';
		},

		// 构建提现参数
		buildWithdrawParams() {
			const platform = this.getCurrentPlatform();
			const isApp = platform === 'app-plus';

			// 基础参数
			const params = {
				amount: this.money,
				type: 4, // 用户分销
				cashToType: this.cashToType
			};

			// 根据提现渠道和平台添加额外参数
			if (this.cashToType === 1) {
				// 微信提现：按照原来的方式，不需要额外参数
			} else if (this.cashToType === 2) {
				// 支付宝提现：需要传手机号、姓名、身份证号
				params.phone = this.alipayPhone;
				params.name = this.realName;
				params.idCode = this.idCode;
			} else if (this.cashToType === 3) {
				// 银行卡提现
				if (isApp) {
					// APP端：cashToType为2或3，如果是2需要传全部信息，3不需要传
					// 根据需求，这里设置为3（不需要传支付宝信息）
					params.cashToType = 3;
				} else {
					// 微信小程序端：银行卡就是线下转账，保持cashToType=3
					params.cashToType = 3;
				}
			}

			return params;
		},

		// 获取提现渠道名称
		getCashToTypeName() {
			switch (this.cashToType) {
				case 1: return '微信';
				case 2: return '支付宝';
				case 3: return '银行卡';
				default: return '微信';
			}
		},

		// 选择提现渠道
		selectCashToType(type) {
			this.cashToType = type;
			this.showCashToType = false;

			// 切换渠道时清空支付宝信息
			if (type !== 2) {
				this.alipayPhone = '';
				this.realName = '';
				this.idCode = '';
			}
		},
		async getMoney() {
			try {
				const res = await this.$api.service.seeTuiMoney();
				this.allmoney = res.data.totalCash || '0';
				this.money = this.allmoney;
			} catch (error) {
				uni.showToast({
					title: '获取可提现金额失败',
					icon: 'none',
				});
			}
		},
		copyPhoneNumber() {
			uni.setClipboardData({
				data: '4008326986',
				success: () => {
					uni.showToast({
						title: '客服电话已复制',
						icon: 'success',
					});
				},
				fail: () => {
					uni.showToast({
						title: '复制失败，请稍后重试',
						icon: 'none',
					});
				}
			});
		},
	},
	onLoad() {
		this.getMoney();
	},
};
</script>

<style scoped lang="scss">
.page {
	background-color: #f8f8f8;
	min-height: 100vh;
	padding: 20rpx 0;
	.header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 28rpx;
		font-weight: 500;
		color: #3b3b3b;
		padding: 0 30rpx;
		width: 750rpx;
		height: 118rpx;
		background: #ffffff;
		.right {
			display: flex;
			align-items: center;
			color: #2e80fe;
			.arrow {
				margin-left: 10rpx;
				font-size: 24rpx;
			}
		}
	}

	.alipay-info {
		margin-top: 20rpx;
		background: #ffffff;
		padding: 30rpx;
		.info-item {
			margin-bottom: 30rpx;
			&:last-child {
				margin-bottom: 0;
			}
			.label {
				font-size: 28rpx;
				font-weight: 500;
				color: #3b3b3b;
				margin-bottom: 20rpx;
			}
		}
	}
	.mid {
		margin-top: 20rpx;
		width: 750rpx;
		height: 276rpx;
		background: #ffffff;
		padding: 0 30rpx;
		padding-top: 40rpx;
		.title {
			font-size: 28rpx;
			font-weight: 500;
			color: #3b3b3b;
		}
		.top {
			display: flex;
			align-items: flex-end;
			justify-content: space-between;
			padding-top: 28rpx;
			padding-bottom: 20rpx;
			border-bottom: 2rpx solid #f2f3f6;
			.r_left {
				font-size: 28rpx;
				font-weight: 500;
				color: #e51837;
			}
		}
		.bottom {
			padding-top: 20rpx;
			font-size: 24rpx;
			font-weight: 500;
			color: #999999;
		}
	}
	.btn {
		margin: 60rpx auto 0;
		width: 690rpx;
		height: 98rpx;
		background: #2e80fe;
		border-radius: 50rpx;
		font-size: 32rpx;
		font-weight: 500;
		color: #ffffff;
		line-height: 98rpx;
		text-align: center;
		&:disabled {
			background: #cccccc;
		}
	}
	.tips {
		display: block;
		font-size: 24rpx;
		font-weight: 500;
		color: #999999;
		text-align: center;
		margin-top: 20rpx;
	}
	.contact {
		display: block;
		font-size: 24rpx;
		font-weight: 500;
		color: #999999;
		text-align: center;
		margin-top: 20rpx;
		.phone {
			color: #2e80fe;
			text-decoration: underline;
		}
	}
}

.cash-type-modal {
	background: #ffffff;
	border-radius: 20rpx 20rpx 0 0;
	padding: 40rpx 30rpx;

	.modal-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 40rpx;

		.modal-title {
			font-size: 32rpx;
			font-weight: 600;
			color: #333333;
		}

		.modal-close {
			font-size: 40rpx;
			color: #999999;
			width: 60rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}

	.cash-type-list {
		.cash-type-item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 30rpx 0;
			border-bottom: 1rpx solid #f5f5f5;

			&:last-child {
				border-bottom: none;
			}

			&.active {
				.type-name {
					color: #2e80fe;
				}
			}

			.type-name {
				font-size: 30rpx;
				color: #333333;
			}

			.type-check {
				font-size: 32rpx;
				color: #2e80fe;
				font-weight: bold;
			}
		}
	}
}
</style>